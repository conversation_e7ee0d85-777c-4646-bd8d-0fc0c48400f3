/**
 * Car configuration with highly tweakable physics parameters
 * Designed for Wipeout-style fast, responsive handling
 */
export const CarConfig = {
    // Physical properties
    physics: {
        mass: 800, // Reduced mass for better acceleration
        dimensions: {
            width: 2.0,
            height: 0.8,
            length: 4.5
        },
        centerOfMass: {
            x: 0,
            y: -0.2, // Lower center of mass for stability
            z: 0.2   // Slightly forward for better handling
        },
        
        // Inertia tensor (affects rotation resistance)
        inertia: {
            x: 800,  // Roll resistance
            y: 1200, // Yaw resistance  
            z: 400   // Pitch resistance
        }
    },

    // Engine and drivetrain
    engine: {
        maxPower: 450, // kW (about 600 HP)
        maxTorque: 800, // Nm
        powerCurve: {
            // RPM -> Power multiplier
            idle: { rpm: 1000, multiplier: 0.1 },
            peak: { rpm: 6000, multiplier: 1.0 },
            redline: { rpm: 8000, multiplier: 0.8 }
        },
        
        // Acceleration characteristics
        acceleration: {
            force: 35000,     // Increased force to overcome friction
            rampUp: 0.8,      // How quickly power builds up
            efficiency: 0.9    // Improved power transfer efficiency
        },
        
        // Braking
        braking: {
            force: 12000,     // Braking force
            distribution: 0.6, // Front/rear brake balance (0.6 = 60% front)
            efficiency: 0.9    // Brake efficiency
        },
        
        // Boost system
        boost: {
            multiplier: 1.8,   // Speed multiplier when boosting
            consumption: 0.33, // Boost consumed per second
            force: 15000,      // Additional force when boosting
            cooldown: 1.0      // Cooldown after boost depleted
        }
    },

    // Suspension and handling
    suspension: {
        // Spring characteristics
        stiffness: 80000,     // Spring stiffness (N/m)
        damping: 4000,        // Damping coefficient
        restLength: 0.4,      // Suspension rest length
        maxTravel: 0.3,       // Maximum suspension travel
        
        // Anti-roll (reduces body roll in corners)
        antiRoll: {
            front: 15000,
            rear: 12000
        },
        
        // Ground adherence (Wipeout-style hover effect)
        hoverHeight: 0.8,     // Target hover height
        hoverForce: 15000,    // Increased force to maintain hover and overcome friction
        hoverDamping: 2000,   // Increased damping for stability
        maxHoverDistance: 2.5 // Maximum distance to apply hover force
    },

    // Steering and control
    steering: {
        maxAngle: 35,         // Maximum steering angle (degrees)
        sensitivity: 1.2,     // Steering input sensitivity
        returnSpeed: 8.0,     // How fast steering returns to center
        
        // Speed-sensitive steering
        speedSensitivity: {
            enabled: true,
            minSpeed: 50,     // km/h - speed where reduction starts
            maxSpeed: 200,    // km/h - speed where minimum sensitivity reached
            minMultiplier: 0.3 // Minimum steering sensitivity at high speed
        },
        
        // Steering assistance
        assistance: {
            enabled: true,
            strength: 0.3,    // How much assistance to apply
            speedThreshold: 30 // Minimum speed for assistance
        }
    },

    // Aerodynamics
    aerodynamics: {
        // Drag forces
        dragCoefficient: 0.15,    // Reduced Cd value for better acceleration
        frontalArea: 1.8,         // Reduced frontal area
        airDensity: 1.225,        // kg/m³
        
        // Downforce (increases with speed²)
        downforce: {
            coefficient: 1.8,      // Downforce coefficient
            distribution: 0.4,     // Front/rear distribution (0.4 = 40% front)
            maxSpeed: 300          // Speed where max downforce reached (km/h)
        },
        
        // Side forces (crosswind, slip)
        sideForce: {
            coefficient: 0.6,
            maxAngle: 15          // Maximum effective slip angle
        }
    },

    // Tire physics
    tires: {
        // Grip characteristics
        grip: {
            longitudinal: 1.4,    // Forward/backward grip
            lateral: 1.6,         // Sideways grip
            combined: 1.2         // Combined grip when both forces applied
        },
        
        // Friction model
        friction: {
            static: 1.1,          // Static friction coefficient
            kinetic: 0.9,         // Kinetic friction coefficient
            rollResistance: 0.005 // Reduced rolling resistance coefficient
        },
        
        // Slip characteristics
        slip: {
            optimalSlip: 0.15,    // Optimal slip ratio for max grip
            maxSlip: 0.8,         // Maximum useful slip ratio
            falloffRate: 2.0      // How quickly grip falls off after optimal slip
        },
        
        // Temperature effects (advanced)
        temperature: {
            enabled: false,       // Enable tire temperature simulation
            optimal: 85,          // Optimal temperature (°C)
            heatRate: 0.1,        // Heat generation rate
            coolRate: 0.05        // Heat dissipation rate
        }
    },

    // Stability and assistance systems
    stability: {
        // Traction control
        tractionControl: {
            enabled: true,
            threshold: 0.3,       // Slip threshold to activate
            reduction: 0.7        // Power reduction when active
        },
        
        // Stability control
        stabilityControl: {
            enabled: true,
            threshold: 0.4,       // Slip angle threshold
            correction: 0.5       // Correction force multiplier
        },
        
        // Anti-lock braking
        abs: {
            enabled: true,
            threshold: 0.25,      // Slip threshold for ABS
            pulseFrequency: 10    // ABS pulse frequency (Hz)
        }
    },

    // Visual and audio feedback
    feedback: {
        // Engine sound
        engineSound: {
            baseFrequency: 80,    // Base engine frequency (Hz)
            rpmMultiplier: 0.1,   // RPM to frequency multiplier
            loadEffect: 0.3       // How much load affects sound
        },
        
        // Visual effects
        effects: {
            exhaustTrail: true,   // Show exhaust trail
            speedLines: true,     // Show speed lines at high speed
            screenShake: 0.5,     // Screen shake intensity
            motionBlur: 0.3       // Motion blur intensity
        }
    }
};
