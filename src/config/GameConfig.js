/**
 * Main game configuration
 * All tweakable parameters for the racing game
 */
export const GameConfig = {
    // Rendering settings
    renderer: {
        antialias: true,
        shadowMapEnabled: true,
        shadowMapType: 'PCFSoftShadowMap', // THREE.PCFSoftShadowMap
        pixelRatio: Math.min(window.devicePixelRatio, 2),
        clearColor: 0x87CEEB, // Sky blue
        fog: {
            enabled: true,
            color: 0x87CEEB,
            near: 100,
            far: 1000
        }
    },

    // Physics settings
    physics: {
        gravity: -15, // Reduced gravity to prevent excessive ground friction
        timeStep: 1/60,
        maxSubSteps: 3,
        broadphase: 'naive', // 'naive' or 'sap'
        solver: {
            iterations: 10,
            tolerance: 0.0001
        }
    },

    // Camera settings
    camera: {
        fov: 75,
        near: 0.1,
        far: 2000,
        // Follow camera settings
        follow: {
            distance: 8,
            height: 4,
            lookAhead: 5,
            smoothness: 0.1,
            rotationSmoothness: 0.05
        },
        // Chase camera settings
        chase: {
            distance: 12,
            height: 6,
            lookAhead: 8,
            smoothness: 0.08,
            rotationSmoothness: 0.03
        }
    },

    // Lighting settings
    lighting: {
        ambient: {
            color: 0x404040,
            intensity: 0.4
        },
        directional: {
            color: 0xffffff,
            intensity: 1.0,
            position: { x: 100, y: 100, z: 50 },
            castShadow: true,
            shadowMapSize: 2048,
            shadowCamera: {
                left: -100,
                right: 100,
                top: 100,
                bottom: -100,
                near: 0.1,
                far: 500
            }
        }
    },

    // Game mechanics
    gameplay: {
        lapCount: 3,
        checkpointTolerance: 15,
        resetHeight: -50, // Reset car if it falls below this
        boostRegenRate: 0.5, // Boost regeneration per second
        maxBoostTime: 3.0, // Maximum boost duration in seconds
    },

    // Performance settings
    performance: {
        targetFPS: 60,
        adaptiveQuality: true,
        maxParticles: 100,
        cullingDistance: 500
    },

    // Debug settings
    debug: {
        enabled: false,
        showPhysics: false,
        showStats: false,
        showWireframe: false,
        logPerformance: false
    }
};
